html {
	tab-size: 4;
}

/* Hide the "Stories" heading */
.sbdocs-h2#stories {
	display: none;
}

.sbdocs-wrapper:not(.x) {
	padding: 24px;
	background-color: transparent;
}

.sbdocs-preview:not(.x) {
	background-color: transparent;
}

/* Typography */
.sbdocs-code:not(.x),
.sbdocs-p:not(.x),
.docblock-argstable:not(#x) {
	font-size: 16px;
	line-height: 28px;
}

.sbdocs-code:not(.x) {
	display: inline;
}

.sbdocs-h3 {
	font-weight: 600;
}

.docblock-argstable:not(#x) {
	table-layout: fixed;
}

/* "name" column */
.docblock-argstable-head:not(#x) th:nth-of-type(1) {
	width: 20%;
}

/* "description" column */
.docblock-argstable-head:not(#x) th:nth-of-type(2) {
	width: 60%;
}

/* "default" column */
.docblock-argstable-head:not(.x) th:nth-of-type(3),
.docblock-argstable-body:not(.x) td:nth-of-type(3) {
	display: none;
}

/* "controls" column */
.docblock-argstable-head:not(#x) th:nth-of-type(4) {
	width: 20%;
}
