{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "storybook dev -p 6006 --no-open", "build": "storybook build"}, "dependencies": {"main": "workspace:*", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "globals": "^16.3.0", "storybook": "^8.6.12", "typescript": "~5.8.3", "vite": "^5.3.5", "@tsconfig/vite-react": "^3.0.2", "@storybook/addon-controls": "8.6.12", "@storybook/addon-docs": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/preview-api": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/theming": "^8.6.12"}}