{"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "minItems": 20, "maxItems": 20, "items": {"type": "object", "properties": {"id": {"type": "string", "faker": "random.uuid"}, "name": {"type": "object", "properties": {"first": {"type": "string", "faker": "name.first<PERSON><PERSON>"}, "last": {"type": "string", "faker": "name.last<PERSON><PERSON>"}}, "required": ["first", "last"]}, "avatar": {"type": "string", "faker": {"fake": "https://robohash.org/{{internet.userName}}.png?size=100x100&set=set1"}}, "MAC": {"type": "string", "faker": "internet.mac"}, "email": {"type": "string", "faker": "internet.email"}, "lastSeen": {"type": "string", "faker": "date.past"}, "materials": {"type": "array", "items": {"type": "string", "faker": "commerce.productMaterial"}, "uniqueItems": true}, "deployed": {"type": "boolean"}, "color": {"type": "string", "faker": "internet.color"}, "note": {"type": "string", "faker": "lorem.paragraph"}}, "additionalProperties": false, "minProperties": 10}}