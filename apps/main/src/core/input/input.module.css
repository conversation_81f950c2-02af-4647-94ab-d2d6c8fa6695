.container {
  position: relative;
  width: 100%;
}

.input {
  width: 100%;
}

.icon {
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  z-index: var(--z-base);
}

/* Follow Button.sizes.large */
.largeMain,
.largeMainWithIcon {
  padding-right: 12px;
  height: 40px;
  font-size: 16px;
}

.largeMain {
  padding-left: 12px;
}

.largeMainWithIcon {
  /* 12 + 16 + 8 */
  padding-left: 36px;
}

.largeIcon {
  left: 12px;
}

.largeColor {
  height: 40px;
  width: 40px;
}

.mediumMain,
.mediumMainWithIcon {
  padding-right: 12px;
  height: 32px;
}

/* Follow Button.sizes.medium */
.mediumMain {
  padding-left: 12px;
}

.mediumMainWithIcon {
  /* 12 + 16 + 8 */
  padding-left: 36px;
}

.mediumIcon {
  left: 12px;
}

.mediumColor {
  height: 32px;
  width: 32px;
}

/* Follow Button.sizes.small */
.smallMain,
.smallMainWithIcon {
  padding-right: 8px;
  height: 24px;
}

.smallMain {
  padding-left: 8px;
}

.smallMainWithIcon {
  /* 8 + 12 + 6 */
  padding-left: 26px;
}

.smallIcon {
  left: 8px;
}

.smallColor {
  height: 24px;
  width: 24px;
}
