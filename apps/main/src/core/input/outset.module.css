.main {
  transition:
    background-color 0.1s,
    outline 0.2s ease-out;

  border-width: 1px;
  border-style: solid;
  --shadow-size: 0px 0.5px 1.5px inset;
}

.main[type="color"]::-webkit-color-swatch-wrapper {
  padding-top: 2px;
  padding-bottom: 2px;
}

:global(.light) .main {
  background-color: var(--white);
  box-shadow: var(--shadow-size) rgba(0, 0, 0, 0.1);
  border-color: var(--gray-2);
}

:global(.dark) .main {
  background-color: var(--gray-9);
  box-shadow: var(--shadow-size) rgba(0, 0, 0, 0.3);
  border-color: var(--black);
}

/* GROUP */

:global(.group-tail) .main {
  border-left: none;
}

/* DISABLED & READ ONLY */

:global(.light) .main:disabled,
:global(.light) .main:read-only {
  background-color: var(--gray-0);
}
:global(.light) .main:disabled {
  color: var(--gray-3);
  border-color: var(--gray-1);
  /* https://stackoverflow.com/questions/262158/disabled-input-text-color */
  -webkit-text-fill-color: var(--gray-3);
  opacity: 1;
}

:global(.dark) .main:disabled,
:global(.dark) .main:read-only {
  background-color: var(--gray-6);
}
:global(.dark) .main:disabled {
  color: var(--gray-4);
  border-color: var(--black);
  /* https://stackoverflow.com/questions/262158/disabled-input-text-color */
  -webkit-text-fill-color: var(--gray-4);
  opacity: 1;
}

/* PLACEHOLDER */

:global(.light) .main::placeholder {
  color: var(--gray-4);
  opacity: 1;
}

:global(.dark) .main::placeholder {
  color: var(--gray-3);
  opacity: 1;
}
