.container {
  display: flex;
  align-items: flex-start;
  line-height: 20px;
  position: relative;
  width: max-content;
}

.input {
  flex: 0 0 auto;
  width: 20px;
  height: 20px;
  appearance: none;
  cursor: pointer;
}

.input:disabled {
  cursor: auto;
}

/* ICON */

.icon {
  position: absolute;
  z-index: var(--z-base);
  /* icon is always 16 x 16 */
  top: 2px;
  left: 2px;
  /* avoid stealing click event on input */
  pointer-events: none;
  /* should be shown based on input:checked */
  opacity: 0;
}

/* LABEL */

.label {
  flex: 1 1 0px;
  cursor: pointer;
  /* Padding instead of margin so cursor all over the control */
  padding-left: 8px;
}

.input:disabled ~ .label {
  cursor: auto;
}
