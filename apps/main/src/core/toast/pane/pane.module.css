.container {
  display: flex;
  align-items: center;
  position: relative;
  width: max-content;
  max-width: 288px;
}

/* This is split to allow extension */
.size {
  padding: 8px 12px;
}

/* Toast always uses a dark background, for all types and themes */
.dark {
  /* Avoid gray-7 and 8 since they are dark's backgrounds */
  background-color: var(--gray-6);
  color: var(--white);
}

.icon {
  flex: 0 0 auto;
}

.children {
  flex: 1 1 0px;
}

.button {
  flex: 0 0 auto;
  /* button's height is 24px while line-height is 16px */
  margin: -4px;
}
