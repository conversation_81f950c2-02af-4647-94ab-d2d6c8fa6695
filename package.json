{"name": "moai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"@main": "pnpm --filter main", "@banhmi": "pnpm --filter ban<PERSON>i", "build": "pnpm @main build && pnpm @banhmi build"}, "packageManager": "pnpm@9.6.0", "dependencies": {"eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-storybook": "^0.8.0", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0"}, "devDependencies": {"prettier": "3.3.3", "@prettier/sync": "^0.5.2"}}